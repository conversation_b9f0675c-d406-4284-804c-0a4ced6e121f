# ==================== 软件配置区域 ====================
# 软件标识和版本号配置 - 方便修改
软件标识 = "123456789012345678"  # 18位软件标识，必须与服务端一致
软件版本号 = "5.0.0"  # 版本号，格式为 x.x.x
服务器地址 = "http://222.186.21.133:8742"
API地址 = f"{服务器地址}/新版网络验证API.php"
# ====================================================

import tkinter as tk
from tkinter import messagebox
import os
import sys
import json
from pathlib import Path
import threading
import requests

try:
    import customtkinter as ctk
    CTK_AVAILABLE = True
except ImportError:
    CTK_AVAILABLE = False


# 导入网络验证模块
try:
    from 模块.网络验证 import 网络验证客户端, 获取机器码, CRYPTO_AVAILABLE, DES加密工具
    NETWORK_AUTH_AVAILABLE = CRYPTO_AVAILABLE
except ImportError as e:
    NETWORK_AUTH_AVAILABLE = False
    print(f"模块导入失败: {e}")

# 导入日志模块
try:
    from 模块.日志 import send_remote_log
    LOG_AVAILABLE = True
except ImportError as e:
    LOG_AVAILABLE = False
    print(f"日志模块导入失败: {e}")



class 登录窗口:
    def __init__(self):
        if CTK_AVAILABLE:
            # 设置customtkinter主题和颜色模式
            ctk.set_appearance_mode("light")  # 浅色模式
            ctk.set_default_color_theme("blue")  # 蓝色主题

            self.root = ctk.CTk()
        else:
            self.root = tk.Tk()

        self.root.title("SteamVault Pro - 登录验证")
        self.root.geometry("450x550")
        self.root.resizable(False, False)

        # 设置窗口居中
        self.center_window()

        # 设置窗口图标
        self.set_window_icon()

        # 配置文件路径
        self.config_file = "登录配置.json"

        # 创建界面
        self.create_widgets()

        # 加载保存的配置
        self.load_config()

        # 登录状态
        self.login_success = False

        # 网络验证客户端
        self.auth_client = None
        self.encryption_tool = None
        if NETWORK_AUTH_AVAILABLE:
            try:
                self.auth_client = 网络验证客户端()
                self.machine_code = self.auth_client.get_machine_code()
                self.encryption_tool = DES加密工具()
            except Exception as e:
                print(f"客户端初始化失败: {e}")
                self.auth_client = None
                self.encryption_tool = None
        else:
            print("功能不可用")
    
    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def get_resource_path(self, relative_path):
        """获取资源文件的绝对路径，兼容PyInstaller打包"""
        try:
            # PyInstaller创建临时文件夹，并将路径存储在_MEIPASS中
            base_path = sys._MEIPASS
        except AttributeError:
            # 如果不是打包环境，使用当前脚本目录
            base_path = os.path.dirname(os.path.abspath(__file__))

        return os.path.join(base_path, relative_path)

    def set_window_icon(self):
        """设置窗口图标"""
        try:
            # 获取图标文件的正确路径
            icon_path = self.get_resource_path("static/icons8-游戏文件夹-50_64x64.ico")

            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
            else:
                # 如果图标文件不存在，尝试使用备用图标
                backup_icon_path = self.get_resource_path("static/favicon.ico")

                if os.path.exists(backup_icon_path):
                    self.root.iconbitmap(backup_icon_path)

        except Exception:
            # 如果设置图标失败，静默忽略
            pass
    
    def create_widgets(self):
        """创建界面组件"""
        if CTK_AVAILABLE:
            self.create_modern_widgets()
        else:
            self.create_classic_widgets()

    def create_modern_widgets(self):
        """创建现代化界面组件（使用CustomTkinter）"""
        # 设置背景渐变色（模拟紫色渐变）
        self.root.configure(fg_color=("#E8E3FF", "#1a1a2e"))

        # 主容器框架 - 添加阴影效果
        main_container = ctk.CTkFrame(
            self.root,
            width=400,
            height=480,
            corner_radius=25,
            fg_color=("white", "#16213e"),
            border_width=1,
            border_color=("#E5E7EB", "#374151")
        )
        main_container.place(relx=0.5, rely=0.5, anchor="center")
        main_container.pack_propagate(False)



        # 标题区域
        title_frame = ctk.CTkFrame(
            main_container,
            height=90,
            corner_radius=20,
            fg_color=("#8B5CF6", "#7C3AED"),  # 紫色渐变
            border_width=0
        )
        title_frame.pack(fill="x", padx=25, pady=(30, 25))
        title_frame.pack_propagate(False)

        # 主标题
        title_label = ctk.CTkLabel(
            title_frame,
            text="SteamVault Pro",
            font=ctk.CTkFont(family="Microsoft YaHei", size=26, weight="bold"),
            text_color="white"
        )
        title_label.place(relx=0.5, rely=0.5, anchor="center")



        # 激活码输入区域
        input_frame = ctk.CTkFrame(
            main_container,
            fg_color="transparent",
            border_width=0
        )
        input_frame.pack(fill="x", padx=30, pady=(0, 15))

        # 激活码标签
        activation_label = ctk.CTkLabel(
            input_frame,
            text="激活码",
            font=ctk.CTkFont(family="Microsoft YaHei", size=14, weight="bold"),
            text_color=("#333333", "#CCCCCC")
        )
        activation_label.pack(anchor="w", pady=(0, 10))

        # 激活码输入框
        self.activation_entry = ctk.CTkEntry(
            input_frame,
            height=50,
            corner_radius=15,
            border_width=2,
            border_color=("#D1D5DB", "#4B5563"),
            fg_color=("#F9FAFB", "#374151"),
            text_color=("#1F2937", "#F9FAFB"),
            placeholder_text="请输入您的激活码",
            placeholder_text_color=("#9CA3AF", "#6B7280"),
            font=ctk.CTkFont(family="Microsoft YaHei", size=13),
            show="*"
        )
        self.activation_entry.pack(fill="x", pady=(0, 18))

        # 添加输入框焦点效果
        def on_entry_focus_in(event):
            if CTK_AVAILABLE:
                self.activation_entry.configure(border_color=("#8B5CF6", "#7C3AED"))

        def on_entry_focus_out(event):
            if CTK_AVAILABLE:
                self.activation_entry.configure(border_color=("#D1D5DB", "#4B5563"))

        self.activation_entry.bind("<FocusIn>", on_entry_focus_in)
        self.activation_entry.bind("<FocusOut>", on_entry_focus_out)

        # 记住激活码复选框
        self.remember_var = tk.BooleanVar()
        remember_checkbox = ctk.CTkCheckBox(
            input_frame,
            text="记住激活码",
            variable=self.remember_var,
            font=ctk.CTkFont(family="Microsoft YaHei", size=12),
            text_color=("#6B7280", "#9CA3AF"),
            fg_color=("#8B5CF6", "#7C3AED"),
            hover_color=("#7C3AED", "#6D28D9"),
            corner_radius=6
        )
        remember_checkbox.pack(anchor="w", pady=(0, 25))

        # 按钮区域
        button_frame = ctk.CTkFrame(
            main_container,
            fg_color="transparent",
            border_width=0
        )
        button_frame.pack(fill="x", padx=30, pady=(0, 25))

        # 登录按钮
        self.login_button = ctk.CTkButton(
            button_frame,
            text="🔐 登录验证",
            height=50,
            corner_radius=15,
            fg_color=("#8B5CF6", "#7C3AED"),
            hover_color=("#7C3AED", "#6D28D9"),
            text_color="white",
            font=ctk.CTkFont(family="Microsoft YaHei", size=14, weight="bold"),
            command=self.login,
            border_width=0
        )
        self.login_button.pack(side="left", fill="x", expand=True, padx=(0, 10))

        # 解绑按钮
        self.unbind_button = ctk.CTkButton(
            button_frame,
            text="🔓 解绑设备",
            height=50,
            corner_radius=15,
            fg_color=("#F59E0B", "#D97706"),
            hover_color=("#D97706", "#B45309"),
            text_color="white",
            font=ctk.CTkFont(family="Microsoft YaHei", size=14, weight="bold"),
            command=self.unbind,
            border_width=0
        )
        self.unbind_button.pack(side="right", fill="x", expand=True, padx=(10, 0))

        # 状态显示区域
        status_frame = ctk.CTkFrame(
            main_container,
            fg_color="transparent",
            border_width=0
        )
        status_frame.pack(fill="x", padx=30, pady=(15, 25))

        # 状态标签
        self.status_label = ctk.CTkLabel(
            status_frame,
            text="请输入激活码进行验证",
            font=ctk.CTkFont(family="Microsoft YaHei", size=12),
            text_color=("#6B7280", "#9CA3AF")
        )
        self.status_label.pack()



        # 绑定回车键到登录按钮
        self.root.bind('<Return>', lambda event: self.login())

        # 设置焦点到激活码输入框
        self.activation_entry.focus()

    def create_classic_widgets(self):
        """创建经典界面组件（使用标准tkinter）"""
        from tkinter import ttk

        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 标题
        title_label = ttk.Label(main_frame, text="SteamVault Pro",
                               font=("Microsoft YaHei", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # 激活码标签
        activation_label = ttk.Label(main_frame, text="激活码:")
        activation_label.grid(row=1, column=0, sticky=tk.W, pady=(0, 10))

        # 激活码输入框
        self.activation_entry = ttk.Entry(main_frame, width=30, show="*")
        self.activation_entry.grid(row=2, column=0, columnspan=2,
                                  sticky=(tk.W, tk.E), pady=(0, 15))

        # 记住激活码复选框
        self.remember_var = tk.BooleanVar()
        remember_checkbox = ttk.Checkbutton(main_frame,
                                          text="记住激活码",
                                          variable=self.remember_var)
        remember_checkbox.grid(row=3, column=0, columnspan=2,
                              sticky=tk.W, pady=(0, 20))

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2,
                         sticky=(tk.W, tk.E), pady=(0, 10))

        # 登录按钮
        self.login_button = ttk.Button(button_frame,
                                      text="登录",
                                      command=self.login,
                                      width=12)
        self.login_button.grid(row=0, column=0, padx=(0, 10))

        # 解绑按钮
        self.unbind_button = ttk.Button(button_frame,
                                       text="解绑",
                                       command=self.unbind,
                                       width=12)
        self.unbind_button.grid(row=0, column=1)

        # 状态标签
        self.status_label = ttk.Label(main_frame, text="",
                                     foreground="blue")
        self.status_label.grid(row=5, column=0, columnspan=2,
                              pady=(10, 0))

        # 配置网格权重
        main_frame.columnconfigure(0, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

        # 绑定回车键到登录按钮
        self.root.bind('<Return>', lambda event: self.login())

        # 设置焦点到激活码输入框
        self.activation_entry.focus()
    
    def load_config(self):
        """加载保存的配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                # 如果记住激活码，则加载激活码
                if config.get('remember_activation', False):
                    self.activation_entry.insert(0, config.get('activation_code', ''))
                    self.remember_var.set(True)
        except:
            # 如果加载配置失败，忽略错误
            pass
    
    def save_config(self):
        """保存配置"""
        try:
            config = {
                'remember_activation': self.remember_var.get(),
                'activation_code': self.activation_entry.get() if self.remember_var.get() else ''
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except:
            # 如果保存配置失败，忽略错误
            pass
    
    def update_status(self, text, color="#6B7280"):
        """更新状态标签"""
        if CTK_AVAILABLE:
            self.status_label.configure(text=text, text_color=color)
        else:
            # 标准tkinter的颜色映射
            tk_color = "blue"
            if color == "#EF4444":
                tk_color = "red"
            elif color == "#F59E0B":
                tk_color = "orange"
            elif color == "#10B981":
                tk_color = "green"
            self.status_label.config(text=text, foreground=tk_color)

    def animate_button_click(self, button):
        """按钮点击动画效果"""
        if CTK_AVAILABLE:
            # 保存原始颜色
            original_color = button.cget("fg_color")
            # 点击效果
            button.configure(fg_color=("#6D28D9", "#5B21B6"))
            self.root.after(100, lambda: button.configure(fg_color=original_color))

    def 执行初始化验证(self):
        """执行初始化验证"""
        try:
            print("正在进行初始化验证...")

            # 检查网络验证是否可用
            if not NETWORK_AUTH_AVAILABLE or not self.encryption_tool:
                raise Exception("网络验证功能不可用")

            # 准备初始化数据
            初始化数据 = {
                'action': 'init',
                'software_id': 软件标识,
                'software_version': 软件版本号
            }

            # 创建加密请求
            安全请求 = self.encryption_tool.create_secure_request(初始化数据)

            # 发送请求
            response = requests.post(
                API地址,
                json=安全请求,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )

            # 解析响应
            响应数据 = response.json()
            if 'encrypted_data' in 响应数据:
                解密响应 = self.encryption_tool.parse_secure_response(响应数据['encrypted_data'])
            else:
                解密响应 = 响应数据

            # 检查验证结果
            if 解密响应.get('status') == 'success':
                print("✓ 初始化验证成功")
                return True
            else:
                error_message = 解密响应.get('message', '初始化验证失败')
                print(f"✗ 初始化验证失败: {error_message}")
                self.处理初始化错误(error_message)
                return False

        except Exception as e:
            error_message = str(e)
            print(f"✗ 初始化验证异常: {error_message}")
            self.处理初始化错误(error_message)
            return False

    def 处理初始化错误(self, 错误信息):
        """处理初始化验证错误"""
        if "软件标识验证失败" in 错误信息:
            # 软件标识不匹配，直接退出
            messagebox.showerror("验证失败", "软件标识验证失败！\n请使用正确的客户端程序。")
            sys.exit(1)

        elif "软件版本不匹配" in 错误信息:
            # 版本不匹配，提示更新
            messagebox.showwarning("需要更新", f"客户端版本过期！\n{错误信息}\n\n程序即将退出，请下载最新版本。")
            sys.exit(1)

        elif "网络" in 错误信息:
            # 网络错误，可以重试
            result = messagebox.askretrycancel("网络错误", f"网络连接失败：{错误信息}\n\n是否重试？")
            if result:
                # 重试初始化验证
                if self.执行初始化验证():
                    return  # 重试成功，继续
                else:
                    sys.exit(1)  # 重试失败，退出
            else:
                sys.exit(1)  # 用户选择不重试，退出

        else:
            # 其他错误
            messagebox.showerror("初始化失败", f"程序初始化失败：{错误信息}\n\n请联系技术支持。")
            sys.exit(1)

    def login(self):
        """登录验证"""
        # 添加按钮点击动画
        self.animate_button_click(self.login_button)

        activation_code = self.activation_entry.get().strip()

        if not activation_code:
            self.update_status("❌ 请输入激活码", "#EF4444")  # 红色
            return

        # 检查网络验证是否可用
        if not NETWORK_AUTH_AVAILABLE or not self.auth_client:
            self.update_status("❌ 网络验证功能不可用", "#EF4444")
            messagebox.showerror("错误", "网络验证功能不可用\n请安装 pycryptodome: pip install pycryptodome")
            return

        # 第一步：执行初始化验证
        self.update_status("🔄 正在进行初始化验证...", "#3B82F6")  # 蓝色
        self.root.update()

        if not self.执行初始化验证():
            # 初始化验证失败，错误处理已在执行初始化验证方法中完成
            return

        # 第二步：显示登录验证状态
        self.update_status("🔄 正在验证激活码...", "#3B82F6")  # 蓝色
        self.root.update()

        # 在后台线程中执行网络验证
        def verify_thread():
            try:
                # 执行登录验证
                response = self.auth_client.login(activation_code)

                # 在主线程中更新UI
                self.root.after(0, lambda resp=response: self.on_login_success_callback(resp))

            except Exception as e:
                # 在主线程中显示错误
                error_message = str(e)
                self.root.after(0, lambda msg=error_message: self.on_login_error_callback(msg))

        # 启动验证线程
        verify_thread_obj = threading.Thread(target=verify_thread, daemon=True)
        verify_thread_obj.start()

    def on_login_success_callback(self, response):
        """登录成功回调"""
        try:
            user_info = response.get('user_info', {})
            remaining_points = user_info.get('remaining_points', 0)

            self.update_status(f"✅ 登录成功！", "#10B981")  # 绿色

            # 记录登录成功日志
            if LOG_AVAILABLE and hasattr(self, 'auth_client') and self.auth_client:
                user_code = self.auth_client.auth_code
                send_remote_log('info', '用户登录成功', {
                    'login_method': 'activation_code',
                    'machine_code': self.machine_code[:16] + '...',
                    'remaining_points': remaining_points,
                    'software_version': 软件版本号
                }, user_code)

            # 显示登录成功信息
            messagebox.showinfo("登录成功",
                              f"激活码验证成功！\n\n"
                              f"⚠️ 重要提醒：\n"
                              f"• 使用入库工具时请不要关闭黑色的运行窗口\n"
                              f"• 关闭运行窗口会导致网页功能失效\n"
                              f"• 使用完毕后可以关闭，不影响已入库的游戏\n"
                              f"• 程序运行不影响电脑任何功能，可放心玩其他网络游戏")

            # 直接调用登录成功处理
            self.on_login_success()

        except Exception as e:
            self.update_status(f"❌ 处理登录响应失败: {e}", "#EF4444")

    def on_login_error_callback(self, error_message):
        """登录失败回调"""
        self.update_status(f"❌ 验证失败: {error_message}", "#EF4444")

        # 记录登录失败日志
        if LOG_AVAILABLE:
            activation_code = self.activation_entry.get().strip()
            user_code = activation_code if activation_code else None
            send_remote_log('warning', '用户登录失败', {
                'login_method': 'activation_code',
                'error_message': error_message,
                'machine_code': self.machine_code[:16] + '...',
                'software_version': 软件版本号
            }, user_code)

        # 显示详细错误信息
        if "激活码无效" in error_message:
            messagebox.showerror("验证失败", "激活码无效或已过期\n请检查激活码是否正确")
        elif "点数不足" in error_message:
            messagebox.showerror("验证失败", "激活码点数不足\n请联系管理员充值")
        elif "已绑定其他设备" in error_message:
            messagebox.showerror("验证失败", "此激活码已绑定其他设备\n请先解绑或联系管理员")
        elif "网络" in error_message or "连接" in error_message:
            messagebox.showerror("网络错误", f"网络连接失败\n{error_message}\n请检查网络连接")
        else:
            messagebox.showerror("验证失败", f"登录验证失败\n{error_message}")

    def unbind(self):
        """解绑操作"""
        # 添加按钮点击动画
        self.animate_button_click(self.unbind_button)

        activation_code = self.activation_entry.get().strip()

        if not activation_code:
            self.update_status("❌ 请输入激活码", "#EF4444")
            return

        # 检查网络验证是否可用
        if not NETWORK_AUTH_AVAILABLE or not self.auth_client:
            self.update_status("❌ 网络验证功能不可用", "#EF4444")
            messagebox.showerror("错误", "网络验证功能不可用")
            return

        # 确认解绑操作
        result = messagebox.askyesno("确认解绑",
                                   f"确定要解绑激活码吗？\n"
                                   f"激活码: {activation_code[:8]}...\n"
                                   f"机器码: {self.machine_code[:16]}...\n\n"
                                   f"解绑后需要重新登录验证")

        if not result:
            return

        # 显示解绑状态
        self.update_status("🔄 正在解绑设备...", "#3B82F6")
        self.root.update()

        # 在后台线程中执行解绑
        def unbind_thread():
            try:
                # 先设置激活码（用于解绑）
                temp_client = 网络验证客户端()
                temp_client.auth_code = activation_code

                # 直接执行解绑（不预先查询，让服务器处理限制检查）
                response = temp_client.unbind_device()

                # 在主线程中更新UI
                self.root.after(0, lambda resp=response: self.on_unbind_success_callback(resp))

            except Exception as e:
                # 在主线程中显示错误
                error_message = str(e)
                self.root.after(0, lambda msg=error_message: self.on_unbind_error_callback(msg))

        # 启动解绑线程
        unbind_thread_obj = threading.Thread(target=unbind_thread, daemon=True)
        unbind_thread_obj.start()

    def on_unbind_success_callback(self, response):
        """解绑成功回调"""
        try:
            unbind_info = response.get('unbind_info', {})
            remaining_unbinds = unbind_info.get('remaining_unbinds', 0)

            self.update_status(f"✅ 解绑成功！剩余解绑次数: {remaining_unbinds}", "#10B981")

            messagebox.showinfo("解绑成功",
                              f"设备解绑成功！\n"
                              f"剩余解绑次数: {remaining_unbinds}\n"
                              f"现在可以在其他设备上使用此激活码")

        except Exception as e:
            self.update_status(f"❌ 处理解绑响应失败: {e}", "#EF4444")

    def on_unbind_error_callback(self, error_message):
        """解绑失败回调"""
        self.update_status(f"❌ 解绑失败: {error_message}", "#EF4444")

        if "今日解绑次数已用完" in error_message:
            messagebox.showerror("解绑失败", "今日解绑次数已用完\n请明天再试或联系管理员")
        elif "激活码无效" in error_message:
            messagebox.showerror("解绑失败", "激活码无效或已过期")
        elif "网络" in error_message or "连接" in error_message:
            messagebox.showerror("网络错误", f"网络连接失败\n{error_message}")
        else:
            messagebox.showerror("解绑失败", f"设备解绑失败\n{error_message}")
    
    def on_login_success(self):
        """登录成功后的处理"""
        # 保存配置
        self.save_config()
        
        # 设置登录状态
        self.login_success = True
        
        # 关闭登录窗口
        self.root.destroy()
    
    def show(self):
        """显示登录窗口"""
        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_window_close)
        
        # 显示窗口
        self.root.mainloop()
        
        # 返回登录状态
        return self.login_success
    
    def on_window_close(self):
        """窗口关闭事件处理"""
        # 清理网络验证客户端
        if self.auth_client and self.auth_client.is_authenticated():
            try:
                self.auth_client.logout()
            except:
                pass  # 忽略登出错误

        # 如果用户直接关闭窗口，退出程序
        self.root.destroy()
        sys.exit(0)


def main():
    """主函数 - 用于测试登录窗口"""
    login_window = 登录窗口()
    success = login_window.show()
    
    if success:
        print("登录成功，启动主程序...")
        # 这里可以调用主程序
    else:
        print("登录失败或用户取消")


if __name__ == "__main__":
    main()
